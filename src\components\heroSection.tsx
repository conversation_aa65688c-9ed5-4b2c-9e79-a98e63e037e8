"use client";

import { motion } from "motion/react";
import { useState, useEffect } from "react";
import Aurora from '@/components/ui/aurora';
  


export default function HeroSection() {
  const [isLoaded, setIsLoaded] = useState(false);

  useEffect(() => {
    setIsLoaded(true);
  }, []);
  return (
    <section className="relative overflow-hidden pt-20">
      <div className="absolute inset-0 z-0">
    <Aurora
      colorStops={["#7CFF67", "#B19EEF", "#5227FF"]}
      blend={0.5}
      amplitude={1.0}
      speed={0.5}
    />
  </div>

      <div className="relative max-w-7xl mx-auto px-6 pt-20 grid gap-12 items-center">
          {/* Left Content */}
          <motion.div
            initial={{ opacity: 0, x: -50 }}
            animate={isLoaded ? { opacity: 1, x: 0 } : { opacity: 0, x: -50 }}
            transition={{ duration: 0.8, ease: "easeOut" }}
            className="text-center lg:text-center"
          >
            <motion.h1
              initial={{ opacity: 0, y: 30 }}
              animate={isLoaded ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
              transition={{ duration: 0.8, delay: 0.2 }}
              className="w-[100%] justify-center items-center text-6xl font-bold text-gray-900 leading-tight mb-6"
            >
              The leading hourly{" "}
              <span className="text-[#FF8228]">housekeeping app</span>{" "}
              in Southeast Asia
            </motion.h1>

            <motion.p
              initial={{ opacity: 0, y: 30 }}
              animate={isLoaded ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
              transition={{ duration: 0.8, delay: 0.4 }}
              className="text-lg text-gray-600 mb-8 max-w-xl mx-auto"
            >
              Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nunc
              vulputate libero et velit interdum, ac aliquet odio mattis.
            </motion.p>

            <motion.button
              initial={{ opacity: 0, y: 30 }}
              animate={isLoaded ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
              transition={{ duration: 0.8, delay: 0.6 }}
              whileHover={{ scale: 1.05, boxShadow: "0 10px 25px rgba(255, 130, 40, 0.3)" }}
              whileTap={{ scale: 0.95 }}
              className="bg-[#FF8228] text-white px-8 py-4 rounded-lg font-semibold text-lg shadow-lg hover:bg-[#e6741f] transition-all duration-300"
            >
              Download the app now
            </motion.button>
          </motion.div>

          {/* Right Content - Phone Mockup */}
          {/* Phone mockup */}
          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            animate={isLoaded ? { opacity: 1, scale: 1 } : { opacity: 0, scale: 0.95 }}
            transition={{ duration: 1, delay: 0.8 }}
            className="translate-y-7 flex justify-center items-center transform hover:rotate-6 transition-transform duration-300"
          >
            <img
              src='/home/<USER>'
              alt="bTaskee App"
              className="w-[50%] h-auto"
            />
            {/* Floating notification */}
          </motion.div>
           
        </div>

 
      {/* Semi-transparent white blur layer with upward gradient */}
      <div className="absolute bottom-0 left-0 right-0 h-80 bg-gradient-to-t from-white to-transparent pointer-events-none" />
    </section>
  );
}