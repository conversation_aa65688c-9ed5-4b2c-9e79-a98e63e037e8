"use client";

import Navigation from "@/components/navigation";
import HeroSection from "@/components/heroSection";
import LogoSlider from "@/components/logoSlider";
import Aurora from "@/components/ui/aurora";
// import { ServicesSection } from "./components/ServicesSection";
// import { FeaturesSection } from "./components/FeaturesSection";
// import { BookingStepsSection } from "./components/BookingStepsSection";
// import { CustomerReviews } from "./components/CustomerReviews";
// import { CallToActionSection } from "./components/CallToActionSection";
// import { FAQSection } from "./components/FAQSection";
// import { Footer } from "./components/Footer";
// import { AboutUsPage } from "./components/AboutUsPage";

// This ensures the page is statically generated at build time
export const dynamic = 'force-static';

export default function Home() {
  return (
    <div className="min-h-screen bg-white relative">
      {/* Aurora background - positioned behind nav and hero */}
      <div
        className="fixed top-0 left-0 right-0 z-0 pointer-events-none"
        style={{ height: '25vw', minHeight: '400px', maxHeight: '600px' }}
      >
        <Aurora
          colorStops={["#4A90E2", "#9B59B6", "#4A90E2"]} // Stronger colors for white background
          blend={0.7}
          amplitude={1.0}
          speed={0.3}
        />
      </div>

      <Navigation />
      <HeroSection />
      <LogoSlider />
      {/* <ServicesSection />
      <FeaturesSection />
      <BookingStepsSection />
      <CustomerReviews />
      <CallToActionSection />
      <FAQSection />
      <Footer /> */}
    </div>
  );
}