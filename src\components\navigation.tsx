"use client";

import { motion } from "motion/react";
import { Globe, ChevronDown } from "lucide-react";
import { useState, useEffect } from "react";

export default function Navigation() {
  const [isLoaded, setIsLoaded] = useState(false);

  useEffect(() => {
    setIsLoaded(true);
  }, []);

  return (
    <motion.nav
      initial={{ y: -20, opacity: 0 }}
      animate={isLoaded ? { y: 0, opacity: 1 } : { y: -20, opacity: 0 }}
      transition={{ duration: 0.6 }}
      className="fixed top-0 left-0 right-0 z-50"
    >
      <div className="max-w-7xl mx-auto px-6 py-4">
        <div className="flex items-center justify-between">
          {/* Logo */}
          <motion.div
            whileHover={{ scale: 1.05 }}
            transition={{ type: "spring", stiffness: 400, damping: 17 }}
            className="cursor-pointer"
          >
            <img
              src='/home/<USER>'
              alt="bTaskee"
              className="h-10 w-auto"
            />
          </motion.div>

          {/* Glass-style Navigation Menu */}
          <div className="hidden lg:flex">
            <motion.div
              initial={{ scale: 0.95, opacity: 0 }}
              animate={isLoaded ? { scale: 1, opacity: 1 } : { scale: 0.95, opacity: 0 }}
              transition={{ duration: 0.5, delay: 0.2 }}
              className="bg-gray-100/80 backdrop-blur-md rounded-full px-8 py-3"
            >
              <div className="flex items-center gap-8">
                <motion.button
                  whileHover={{ y: -2 }}
                  transition={{ type: "spring", stiffness: 400, damping: 17 }}
                  className="text-gray-800 hover:text-[#FF8228] transition-colors duration-200 relative"
                >
                  <span className="flex items-center gap-1">
                    Home
                  </span>
                  <motion.div
                    className="absolute -bottom-3 left-0 right-0 h-0.5 bg-[#FF8228] rounded-full"
                  />
                </motion.button>
              </div>
            </motion.div>
          </div>

          {/* Language and Country Icons */}
          <motion.div
            initial={{ x: 20, opacity: 0 }}
            animate={isLoaded ? { x: 0, opacity: 1 } : { x: 20, opacity: 0 }}
            transition={{ duration: 0.5, delay: 0.3 }}
            className="flex items-center space-x-4"
          >
            <motion.button
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.95 }}
              className="flex items-center space-x-2 text-gray-600 hover:text-[#FF8228] transition-colors"
            >
              <div className="w-6 h-4 bg-red-600 relative rounded-sm overflow-hidden">
                <div className="absolute inset-0 flex items-center justify-center">
                  <div className="w-3 h-3 border border-yellow-400">
                    <div className="w-full h-full bg-yellow-400 transform rotate-45 scale-75"></div>
                  </div>
                </div>
              </div>
              <span className="text-sm font-medium">VIE</span>
              <ChevronDown className="w-4 h-4" />
            </motion.button>

            <motion.button
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.95 }}
              className="text-gray-600 hover:text-[#FF8228] transition-colors"
            >
              <Globe className="w-5 h-5" />
            </motion.button>
          </motion.div>

          {/* Mobile Menu Button */}
          <motion.button
            initial={{ opacity: 0 }}
            animate={isLoaded ? { opacity: 1 } : { opacity: 0 }}
            transition={{ duration: 0.5, delay: 0.4 }}
            className="lg:hidden flex items-center justify-center w-10 h-10 rounded-lg bg-gray-100/80 backdrop-blur-md text-gray-600 hover:text-[#FF8228] transition-colors"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
            </svg>
          </motion.button>
        </div>
      </div>
    </motion.nav>
  );
}