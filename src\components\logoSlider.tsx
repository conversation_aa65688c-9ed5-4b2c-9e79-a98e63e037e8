"use client";
import { motion } from "motion/react";
import { useEffect, useState } from "react";
import { 
  type CarouselApi 
} from "./ui/carousel";

export default function LogoSlider() {
  const [api, setApi] = useState<CarouselApi>();
  const [current, setCurrent] = useState(0);

  const logos = [
    { src: "/home/<USER>", alt: "Saigon Times", width: "w-32" },
    { src: "/home/<USER>", alt: "VTC8", width: "w-20" },
    { src: "/home/<USER>", alt: "<PERSON><PERSON><PERSON>", width: "w-36" },
    { src: "/home/<USER>", alt: "Vietnam Advisors", width: "w-28" },
    { src: "/home/<USER>", alt: "Saigon Times", width: "w-32" },
    { src: "/home/<USER>", alt: "VTC8", width: "w-20" },
    { src: "/home/<USER>", alt: "<PERSON><PERSON><PERSON>", width: "w-36" },
    { src: "/home/<USER>", alt: "Vietnam Advisors", width: "w-28" },
  ];

  // Track current slide for visual indicators
  useEffect(() => {
    if (!api) return;

    const onSelect = () => {
      setCurrent(api.selectedScrollSnap());
    };

    api.on("select", onSelect);
    onSelect();

    return () => {
      api.off("select", onSelect);
    };
  }, [api]);

  // Continuous auto-play functionality - never pauses
  useEffect(() => {
    if (!api) return;

    const autoPlay = setInterval(() => {
      api.scrollNext();
    }, 1000); // Scroll every 2 seconds for smooth continuous movement

    // Never clear the interval - continuous play
    return () => clearInterval(autoPlay);
  }, [api]);

  const loopedLogos = [...logos, ...logos];

  return (
    <section className="py-16 bg-white overflow-hidden">
      <div className="max-w-7xl mx-auto px-6">
        <h3 className="text-center text-gray-600 mb-8 font-medium">
          Trusted by leading media partners
        </h3>

        <div className="relative overflow-hidden">
          <motion.div
            animate={{ x: ["0%", "-50%"] }} // di chuyển một nửa (vì nhân đôi logo)
            transition={{
              duration: 30,  // điều chỉnh tốc độ
              repeat: Infinity,
              ease: "linear",
            }}
            className="flex items-center space-x-16 w-[200%]" // gấp đôi chiều rộng
          >
            {loopedLogos.map((logo, index) => (
              <div
                key={index}
                className="flex-shrink-0 flex items-center justify-center h-16"
              >
                <img
                  src={logo.src}
                  alt={logo.alt}
                  className={`${logo.width} h-auto max-h-12 object-contain`}
                />
              </div>
            ))}
          </motion.div>
        </div>
      </div>
    </section>
  );
}